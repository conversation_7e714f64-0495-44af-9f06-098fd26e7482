// src/scripts/seed-master-data.ts
/**
 * <PERSON><PERSON><PERSON> to seed master data tables in Supabase
 * Run this script to populate classes, sections, academic_years, and guardian_relations tables
 */

// Load environment variables from .env file
import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';
import { resolve } from 'path';

// Load .env file from project root
config({ path: resolve(process.cwd(), '.env') });

// Create Supabase client directly with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please ensure NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY are set in .env file');
  process.exit(1);
}

// Use anon key for now - we'll handle RLS later
const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database table constants
const DATABASE_TABLES = {
  CLASSES: 'classes',
  SECTIONS: 'sections',
  ACADEMIC_YEARS: 'academic_years',
  GUARDIAN_RELATIONS: 'guardian_relations'
};

/**
 * Seed classes table
 */
async function seedClasses() {
  console.log('🌱 Seeding classes table...');
  
  const classes = [
    { name: 'Nursery', description: 'Nursery Class for ages 3-4', is_active: true },
    { name: 'LKG', description: 'Lower Kindergarten for ages 4-5', is_active: true },
    { name: 'UKG', description: 'Upper Kindergarten for ages 5-6', is_active: true },
    { name: 'Class 1', description: 'First Standard for ages 6-7', is_active: true },
    { name: 'Class 2', description: 'Second Standard for ages 7-8', is_active: true },
    { name: 'Class 3', description: 'Third Standard for ages 8-9', is_active: true },
    { name: 'Class 4', description: 'Fourth Standard for ages 9-10', is_active: true },
    { name: 'Class 5', description: 'Fifth Standard for ages 10-11', is_active: true },
    { name: 'Class 6', description: 'Sixth Standard for ages 11-12', is_active: true },
    { name: 'Class 7', description: 'Seventh Standard for ages 12-13', is_active: true },
    { name: 'Class 8', description: 'Eighth Standard for ages 13-14', is_active: true },
    { name: 'Class 9', description: 'Ninth Standard for ages 14-15', is_active: true },
    { name: 'Class 10', description: 'Tenth Standard for ages 15-16', is_active: true }
  ];

  // Check if classes already exist
  const { data: existingClasses } = await supabase
    .from(DATABASE_TABLES.CLASSES)
    .select('name');

  const existingClassNames = new Set(existingClasses?.map(c => c.name) || []);
  const newClasses = classes.filter(c => !existingClassNames.has(c.name));

  if (newClasses.length === 0) {
    console.log('✅ All classes already exist, skipping...');
    return existingClasses;
  }

  const { data, error } = await supabase
    .from(DATABASE_TABLES.CLASSES)
    .insert(newClasses)
    .select();

  if (error) {
    console.error('❌ Error seeding classes:', error);
    throw error;
  }

  console.log(`✅ Successfully seeded ${data?.length || 0} classes`);
  return data;
}

/**
 * Seed sections table
 */
async function seedSections() {
  console.log('🌱 Seeding sections table...');
  
  const sections = [
    { name: 'A', description: 'Section A', is_active: true },
    { name: 'B', description: 'Section B', is_active: true },
    { name: 'C', description: 'Section C', is_active: true },
    { name: 'D', description: 'Section D', is_active: true },
    { name: 'E', description: 'Section E', is_active: true }
  ];

  // Check if sections already exist
  const { data: existingSections } = await supabase
    .from(DATABASE_TABLES.SECTIONS)
    .select('name');

  const existingSectionNames = new Set(existingSections?.map(s => s.name) || []);
  const newSections = sections.filter(s => !existingSectionNames.has(s.name));

  if (newSections.length === 0) {
    console.log('✅ All sections already exist, skipping...');
    return existingSections;
  }

  const { data, error } = await supabase
    .from(DATABASE_TABLES.SECTIONS)
    .insert(newSections)
    .select();

  if (error) {
    console.error('❌ Error seeding sections:', error);
    throw error;
  }

  console.log(`✅ Successfully seeded ${data?.length || 0} sections`);
  return data;
}

/**
 * Seed academic_years table
 */
async function seedAcademicYears() {
  console.log('🌱 Seeding academic_years table...');
  
  const currentYear = new Date().getFullYear();
  const academicYears = [
    {
      year: `${currentYear - 1}-${currentYear}`,
      start_date: `${currentYear - 1}-04-01`,
      end_date: `${currentYear}-03-31`,
      is_current: false,
      is_active: true
    },
    {
      year: `${currentYear}-${currentYear + 1}`,
      start_date: `${currentYear}-04-01`,
      end_date: `${currentYear + 1}-03-31`,
      is_current: true,
      is_active: true
    },
    {
      year: `${currentYear + 1}-${currentYear + 2}`,
      start_date: `${currentYear + 1}-04-01`,
      end_date: `${currentYear + 2}-03-31`,
      is_current: false,
      is_active: true
    }
  ];

  // Check if academic years already exist
  const { data: existingYears } = await supabase
    .from(DATABASE_TABLES.ACADEMIC_YEARS)
    .select('year');

  const existingYearNames = new Set(existingYears?.map(y => y.year) || []);
  const newAcademicYears = academicYears.filter(y => !existingYearNames.has(y.year));

  if (newAcademicYears.length === 0) {
    console.log('✅ All academic years already exist, skipping...');
    return existingYears;
  }

  const { data, error } = await supabase
    .from(DATABASE_TABLES.ACADEMIC_YEARS)
    .insert(newAcademicYears)
    .select();

  if (error) {
    console.error('❌ Error seeding academic years:', error);
    throw error;
  }

  console.log(`✅ Successfully seeded ${data?.length || 0} academic years`);
  return data;
}

/**
 * Seed guardian_relations table
 */
async function seedGuardianRelations() {
  console.log('🌱 Seeding guardian_relations table...');
  
  const guardianRelations = [
    { name: 'Father', description: 'Biological father', is_active: true },
    { name: 'Mother', description: 'Biological mother', is_active: true },
    { name: 'Guardian', description: 'Legal guardian', is_active: true },
    { name: 'Uncle', description: 'Uncle (paternal or maternal)', is_active: true },
    { name: 'Aunt', description: 'Aunt (paternal or maternal)', is_active: true },
    { name: 'Grandfather', description: 'Grandfather (paternal or maternal)', is_active: true },
    { name: 'Grandmother', description: 'Grandmother (paternal or maternal)', is_active: true },
    { name: 'Brother', description: 'Elder brother', is_active: true },
    { name: 'Sister', description: 'Elder sister', is_active: true },
    { name: 'Other', description: 'Other relation', is_active: true }
  ];

  // Check if guardian relations already exist
  const { data: existingRelations } = await supabase
    .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
    .select('name');

  const existingRelationNames = new Set(existingRelations?.map(r => r.name) || []);
  const newGuardianRelations = guardianRelations.filter(r => !existingRelationNames.has(r.name));

  if (newGuardianRelations.length === 0) {
    console.log('✅ All guardian relations already exist, skipping...');
    return existingRelations;
  }

  const { data, error } = await supabase
    .from(DATABASE_TABLES.GUARDIAN_RELATIONS)
    .insert(newGuardianRelations)
    .select();

  if (error) {
    console.error('❌ Error seeding guardian relations:', error);
    throw error;
  }

  console.log(`✅ Successfully seeded ${data?.length || 0} guardian relations`);
  return data;
}

/**
 * Main seeding function
 */
async function seedMasterData() {
  try {
    console.log('🚀 Starting master data seeding...');
    console.log('📊 This will populate the following tables:');
    console.log('   - classes');
    console.log('   - sections');
    console.log('   - academic_years');
    console.log('   - guardian_relations');
    console.log('');

    // Seed all master data tables
    await seedClasses();
    await seedSections();
    await seedAcademicYears();
    await seedGuardianRelations();

    console.log('');
    console.log('🎉 Master data seeding completed successfully!');
    console.log('✅ All tables have been populated with default data');
    console.log('🔄 The student wizard should now load data from Supabase tables');
    
  } catch (error) {
    console.error('💥 Master data seeding failed:', error);
    process.exit(1);
  }
}

/**
 * Check if tables exist and have data
 */
async function checkMasterData() {
  console.log('🔍 Checking current master data...');
  
  try {
    const [classesResult, sectionsResult, academicYearsResult, guardianRelationsResult] = await Promise.allSettled([
      supabase.from(DATABASE_TABLES.CLASSES).select('count', { count: 'exact', head: true }),
      supabase.from(DATABASE_TABLES.SECTIONS).select('count', { count: 'exact', head: true }),
      supabase.from(DATABASE_TABLES.ACADEMIC_YEARS).select('count', { count: 'exact', head: true }),
      supabase.from(DATABASE_TABLES.GUARDIAN_RELATIONS).select('count', { count: 'exact', head: true })
    ]);

    console.log('📊 Current data counts:');
    console.log(`   Classes: ${classesResult.status === 'fulfilled' ? classesResult.value.count || 0 : 'Error'}`);
    console.log(`   Sections: ${sectionsResult.status === 'fulfilled' ? sectionsResult.value.count || 0 : 'Error'}`);
    console.log(`   Academic Years: ${academicYearsResult.status === 'fulfilled' ? academicYearsResult.value.count || 0 : 'Error'}`);
    console.log(`   Guardian Relations: ${guardianRelationsResult.status === 'fulfilled' ? guardianRelationsResult.value.count || 0 : 'Error'}`);
    
  } catch (error) {
    console.error('❌ Error checking master data:', error);
  }
}

// Export functions for use in other scripts
export { checkMasterData, seedAcademicYears, seedClasses, seedGuardianRelations, seedMasterData, seedSections };

// Run the script if called directly
if (require.main === module) {
  (async () => {
    await checkMasterData();
    console.log('');
    await seedMasterData();
  })();
}
