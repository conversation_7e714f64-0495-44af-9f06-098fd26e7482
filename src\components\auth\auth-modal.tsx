// src/components/auth/auth-modal.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'signup';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'signup' }: AuthModalProps) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key and focus management
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);

      // Focus the modal when it opens
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (modal) {
        modal.focus();
      }

      // Trap focus within modal
      const focusableElements = modal?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        const handleTabKey = (e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        };

        document.addEventListener('keydown', handleTabKey);

        return () => {
          document.removeEventListener('keydown', handleEscape);
          document.removeEventListener('keydown', handleTabKey);
        };
      }
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };  return (
    <div
      className="fixed inset-0 bg-slate-900/50 flex items-center justify-center backdrop-blur-sm animate-modalBackdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="auth-modal-title"
      tabIndex={-1}
      style={{
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif",
        zIndex: 10000, // Higher z-index to ensure it's above everything
        padding: '1rem' // Ensure proper padding on all sides
      }}
    >
      <div
        className="bg-white rounded-2xl shadow-2xl border border-slate-200 w-full max-w-md mx-auto"
        style={{
          maxHeight: 'calc(100vh - 2rem)', // Ensure modal fits in viewport with margin
          overflowY: 'auto'
        }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute text-slate-400 hover:text-slate-600 transition-all duration-200 z-20 hover:bg-slate-100 rounded-full"
          style={{
            top: '1rem',
            right: '1rem',
            padding: '0.5rem',
            width: '2.5rem',
            height: '2.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          aria-label="Close authentication modal"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal Header */}
        <div className="bg-white border-b border-slate-200 rounded-t-2xl" style={{ padding: '2rem 2rem 1.5rem 2rem' }}>
          <div className="text-center">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center mb-6" style={{ gap: '0.75rem' }}>
              <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg" style={{
                width: '2.5rem',
                height: '2.5rem'
              }}>
                <svg className="text-white" style={{ width: '1.5rem', height: '1.5rem' }} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h2 id="auth-modal-title" className="text-2xl-app font-bold text-slate-900">EduPro</h2>
            </div>

            {/* Tab Toggle */}
            <div className="flex bg-slate-100 rounded-xl border border-slate-200" style={{ padding: '0.25rem' }}>
              <button
                onClick={() => handleToggle('signup')}
                type="button"
                className={`flex-1 rounded-lg text-sm-app font-semibold transition-all duration-200 ${
                  activeTab === 'signup'
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-slate-600 hover:text-slate-800'
                }`}
                style={{ padding: '0.75rem 1rem' }}
                aria-pressed={activeTab === 'signup'}
                aria-label="Switch to sign up form"
              >
                Sign Up
              </button>
              <button
                onClick={() => handleToggle('login')}
                type="button"
                className={`flex-1 rounded-lg text-sm-app font-semibold transition-all duration-200 ${
                  activeTab === 'login'
                    ? 'bg-white text-indigo-600 shadow-sm'
                    : 'text-slate-600 hover:text-slate-800'
                }`}
                style={{ padding: '0.75rem 1rem' }}
                aria-pressed={activeTab === 'login'}
                aria-label="Switch to sign in form"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>        {/* Modal Body */}
        <div className="bg-white rounded-b-2xl" style={{
          maxHeight: 'calc(100vh - 12rem)', // Ensure content doesn't overflow viewport
          padding: '2rem'
        }}>
          <div className="text-center" style={{ marginBottom: '2rem' }}>
            <h3 className="text-xl-app font-bold text-slate-900" style={{ marginBottom: '0.5rem' }}>
              {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
            </h3>
            <p className="text-slate-600 text-sm-app">
              {activeTab === 'signup'
                ? 'Join thousands of educators and start your journey with EduPro'
                : 'Sign in to continue your learning journey'
              }
            </p>
          </div>
          <AuthForm formType={activeTab} onToggleForm={handleToggle} />
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
