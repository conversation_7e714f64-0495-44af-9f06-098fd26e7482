// src/components/auth/auth-modal.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'signup';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'signup' }: AuthModalProps) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key and focus management
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);

      // Focus the modal when it opens
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (modal) {
        modal.focus();
      }

      // Trap focus within modal
      const focusableElements = modal?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        const handleTabKey = (e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        };

        document.addEventListener('keydown', handleTabKey);

        return () => {
          document.removeEventListener('keydown', handleEscape);
          document.removeEventListener('keydown', handleTabKey);
        };
      }
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };  return (
    <div
      className="fixed inset-0 bg-slate-900/60 flex items-center justify-center backdrop-blur-sm animate-modalBackdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="auth-modal-title"
      tabIndex={-1}
      style={{
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif",
        zIndex: 10000,
        padding: '1rem'
      }}
    >
      <div
        className="bg-white rounded-xl shadow-2xl border border-slate-200/50 w-full max-w-sm mx-auto overflow-hidden"
        style={{
          maxHeight: 'calc(100vh - 2rem)',
          minHeight: '480px' // Ensure consistent height for both forms
        }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 text-slate-400 hover:text-slate-600 hover:bg-slate-100 rounded-lg p-1.5 transition-all duration-200 z-20"
          aria-label="Close authentication modal"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal Header */}
        <div className="bg-gradient-to-r from-slate-50 to-slate-100/50 border-b border-slate-200/60 px-6 pt-6 pb-4">
          <div className="text-center">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center mb-4 gap-2">
              <div className="bg-gradient-to-br from-indigo-600 to-indigo-700 rounded-lg flex items-center justify-center shadow-md w-8 h-8">
                <svg className="text-white w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h2 id="auth-modal-title" className="text-xl font-bold text-slate-900">EduPro</h2>
            </div>

            {/* Tab Toggle */}
            <div className="flex bg-white rounded-lg border border-slate-200 shadow-sm p-1">
              <button
                onClick={() => handleToggle('signup')}
                type="button"
                className={`flex-1 rounded-md text-sm font-medium transition-all duration-200 py-2 px-3 ${
                  activeTab === 'signup'
                    ? 'bg-indigo-600 text-white shadow-sm'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-slate-50'
                }`}
                aria-pressed={activeTab === 'signup'}
                aria-label="Switch to sign up form"
              >
                Sign Up
              </button>
              <button
                onClick={() => handleToggle('login')}
                type="button"
                className={`flex-1 rounded-md text-sm font-medium transition-all duration-200 py-2 px-3 ${
                  activeTab === 'login'
                    ? 'bg-indigo-600 text-white shadow-sm'
                    : 'text-slate-600 hover:text-slate-800 hover:bg-slate-50'
                }`}
                aria-pressed={activeTab === 'login'}
                aria-label="Switch to sign in form"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>        {/* Modal Body */}
        <div className="bg-white px-6 py-5 flex-1 overflow-y-auto">
          <div className="text-center mb-5">
            <h3 className="text-lg font-semibold text-slate-900 mb-1">
              {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
            </h3>
            <p className="text-slate-600 text-sm">
              {activeTab === 'signup'
                ? 'Join thousands of educators worldwide'
                : 'Sign in to continue your journey'
              }
            </p>
          </div>
          <AuthForm formType={activeTab} onToggleForm={handleToggle} />
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
