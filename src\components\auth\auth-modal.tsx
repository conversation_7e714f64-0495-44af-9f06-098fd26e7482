// src/components/auth/auth-modal.tsx
'use client';

import { useEffect, useState } from 'react';
import AuthForm from './auth-form';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultTab?: 'login' | 'signup';
}

const AuthModal = ({ isOpen, onClose, defaultTab = 'signup' }: AuthModalProps) => {
  const [activeTab, setActiveTab] = useState<'login' | 'signup'>(defaultTab);

  useEffect(() => {
    setActiveTab(defaultTab);
  }, [defaultTab, isOpen]);

  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Handle escape key and focus management
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape' && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscape);

      // Focus the modal when it opens
      const modal = document.querySelector('[role="dialog"]') as HTMLElement;
      if (modal) {
        modal.focus();
      }

      // Trap focus within modal
      const focusableElements = modal?.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );

      if (focusableElements && focusableElements.length > 0) {
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        const handleTabKey = (e: KeyboardEvent) => {
          if (e.key === 'Tab') {
            if (e.shiftKey) {
              if (document.activeElement === firstElement) {
                e.preventDefault();
                lastElement.focus();
              }
            } else {
              if (document.activeElement === lastElement) {
                e.preventDefault();
                firstElement.focus();
              }
            }
          }
        };

        document.addEventListener('keydown', handleTabKey);

        return () => {
          document.removeEventListener('keydown', handleEscape);
          document.removeEventListener('keydown', handleTabKey);
        };
      }
    }

    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const handleToggle = (tab: 'login' | 'signup') => {
    setActiveTab(tab);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };  return (
    <div
      className="fixed inset-0 bg-gradient-to-br from-slate-900/95 via-slate-800/95 to-slate-900/95 flex items-center justify-center backdrop-blur-lg animate-modalBackdrop"
      onClick={handleBackdropClick}
      role="dialog"
      aria-modal="true"
      aria-labelledby="auth-modal-title"
      tabIndex={-1}
      style={{
        fontFamily: "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif",
        zIndex: 10000, // Higher z-index to ensure it's above everything
        padding: '1rem' // Ensure proper padding on all sides
      }}
    >
      <div
        className="bg-white/98 backdrop-blur-xl rounded-2xl w-full max-w-md mx-auto relative shadow-2xl border border-white/30 animate-slideIn"
        style={{
          maxHeight: 'calc(100vh - 2rem)', // Ensure modal fits in viewport with margin
          overflowY: 'auto',
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1)'
        }}
      >
        {/* Close Button */}
        <button
          onClick={onClose}
          className="absolute text-slate-400 hover:text-slate-700 transition-all duration-200 z-20 hover:bg-slate-100/90 rounded-full backdrop-blur-sm"
          style={{
            top: '1rem', // Better positioning
            right: '1rem', // Better positioning
            padding: '0.5rem',
            width: '2.5rem',
            height: '2.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
          aria-label="Close authentication modal"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>

        {/* Modal Header */}
        <div className="bg-gradient-to-br from-white/98 to-slate-50/98 backdrop-blur-sm border-b border-slate-200/50 rounded-t-2xl" style={{ padding: '2rem 2rem 1.5rem 2rem' }}>
          <div className="text-center">
            {/* Logo and Brand */}
            <div className="flex items-center justify-center mb-5" style={{ gap: '0.625rem' }}>
              <div className="bg-gradient-to-br from-indigo-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg shadow-indigo-500/25" style={{
                width: '2.25rem', // 36px in rem
                height: '2.25rem' // 36px in rem
              }}>
                <svg className="text-white" style={{ width: '1.25rem', height: '1.25rem' }} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                </svg>
              </div>
              <h2 id="auth-modal-title" className="text-xl-app font-bold text-slate-900">EduPro</h2>
            </div>

            {/* Tab Toggle */}
            <div className="flex bg-slate-100/90 backdrop-blur-sm rounded-xl shadow-inner border border-slate-200/50" style={{ padding: '0.375rem' }}>
              <button
                onClick={() => handleToggle('signup')}
                type="button"
                className={`flex-1 rounded-lg text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'signup'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-white/70'
                }`}
                style={{ padding: '0.625rem 1rem' }} // 10px 16px in rem
                aria-pressed={activeTab === 'signup'}
                aria-label="Switch to sign up form"
              >
                Sign Up
              </button>
              <button
                onClick={() => handleToggle('login')}
                type="button"
                className={`flex-1 rounded-lg text-sm-app font-semibold transition-all duration-300 ${
                  activeTab === 'login'
                    ? 'bg-gradient-to-r from-indigo-600 to-purple-600 text-white shadow-lg shadow-indigo-500/25 transform scale-[1.02]'
                    : 'text-slate-700 hover:text-slate-900 hover:bg-white/70'
                }`}
                style={{ padding: '0.625rem 1rem' }} // 10px 16px in rem
                aria-pressed={activeTab === 'login'}
                aria-label="Switch to sign in form"
              >
                Sign In
              </button>
            </div>
          </div>
        </div>        {/* Modal Body */}
        <div className="overflow-y-auto bg-gradient-to-br from-indigo-50/40 via-purple-50/30 to-pink-50/40 backdrop-blur-sm relative rounded-b-2xl" style={{
          maxHeight: 'calc(100vh - 12rem)', // Ensure content doesn't overflow viewport
          padding: '2rem'
        }}>
          {/* Subtle overlay pattern */}
          <div className="absolute inset-0 bg-gradient-to-tr from-blue-50/10 via-transparent to-violet-50/10 pointer-events-none rounded-b-2xl"></div>
          <div className="relative z-10">
            <div className="text-center" style={{ marginBottom: '1.5rem' }}>
              <h3 className="text-xl font-bold text-slate-900" style={{ marginBottom: '0.75rem' }}>
                {activeTab === 'signup' ? 'Create Your Account' : 'Welcome Back'}
              </h3>
              <p className="text-slate-600 text-sm leading-relaxed">
                {activeTab === 'signup'
                  ? 'Join thousands of educators worldwide and unlock premium features'
                  : 'Sign in to continue your learning journey with EduPro'
                }
              </p>
            </div>
            <AuthForm formType={activeTab} onToggleForm={handleToggle} />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthModal;
