// src/hooks/useMasterData.ts
import { useEffect, useState } from 'react';
import { MasterDataService } from '../services/masterDataService';
import type { AcademicYear, Class, GuardianRelation, Section } from '../types/database';

interface MasterData {
  classes: Class[];
  sections: Section[];
  academicYears: AcademicYear[];
  guardianRelations: GuardianRelation[];
  currentAcademicYear: AcademicYear | null;
}

interface UseMasterDataReturn {
  data: MasterData | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

export const useMasterData = (): UseMasterDataReturn => {
  const [data, setData] = useState<MasterData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fallback data function
  const getFallbackData = (): MasterData => {
    const currentYear = new Date().getFullYear();
    return {
      classes: [
        { id: '1', name: 'Nursery', description: 'Nursery Class', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '2', name: 'LKG', description: 'Lower Kindergarten', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '3', name: 'UKG', description: 'Upper Kindergarten', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '4', name: 'Class 1', description: 'First Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '5', name: 'Class 2', description: 'Second Standard', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
      ],
      sections: [
        { id: '1', name: 'A', description: 'Section A', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '2', name: 'B', description: 'Section B', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '3', name: 'C', description: 'Section C', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '4', name: 'D', description: 'Section D', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
      ],
      academicYears: [
        {
          id: '1',
          name: `${currentYear}-${currentYear + 1}`,
          start_date: `${currentYear}-04-01`,
          end_date: `${currentYear + 1}-03-31`,
          is_current: true,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          name: `${currentYear - 1}-${currentYear}`,
          start_date: `${currentYear - 1}-04-01`,
          end_date: `${currentYear}-03-31`,
          is_current: false,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      ],
      guardianRelations: [
        { id: '1', name: 'Father', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '2', name: 'Mother', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '3', name: 'Guardian', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '4', name: 'Uncle', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() },
        { id: '5', name: 'Aunt', is_active: true, created_at: new Date().toISOString(), updated_at: new Date().toISOString() }
      ],
      currentAcademicYear: {
        id: '1',
        name: `${currentYear}-${currentYear + 1}`,
        start_date: `${currentYear}-04-01`,
        end_date: `${currentYear + 1}-03-31`,
        is_current: true,
        is_active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    };
  };

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);

      console.log('🔄 Fetching master data...');

      // Add timeout to prevent infinite loading - shorter timeout for better UX
      const timeoutPromise = new Promise<never>((_, reject) => {
        setTimeout(() => reject(new Error('Master data fetch timeout - using fallback data')), 5000); // 5 second timeout
      });

      const masterData = await Promise.race([
        MasterDataService.getAllMasterData(),
        timeoutPromise
      ]);

      console.log('✅ Master data fetched successfully:', {
        classes: masterData.classes?.length || 0,
        sections: masterData.sections?.length || 0,
        academicYears: masterData.academicYears?.length || 0,
        guardianRelations: masterData.guardianRelations?.length || 0,
        currentAcademicYear: masterData.currentAcademicYear?.name || 'None'
      });

      setData(masterData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'An unknown error occurred';
      console.error('❌ Error fetching master data:', err);

      // Instead of setting error, provide fallback data
      console.warn('⚠️  Using fallback master data due to error:', errorMessage);

      const fallbackData = getFallbackData();
      setData(fallbackData);
      // Don't set error, just log it
      console.log('✅ Fallback master data loaded successfully');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    // Load fallback data immediately to prevent hanging
    const fallbackData = getFallbackData();
    setData(fallbackData);
    setLoading(false);
    console.log('🚀 Immediate fallback data loaded - wizard can open');

    // Try to fetch real data in background with shorter timeout
    const fetchRealData = async () => {
      try {
        console.log('🔄 Attempting to fetch real master data in background...');
        const realData = await MasterDataService.getAllMasterData();

        // Only update if we got more data than fallback
        if (realData.classes.length > fallbackData.classes.length ||
            realData.sections.length > fallbackData.sections.length ||
            realData.academicYears.length > fallbackData.academicYears.length ||
            realData.guardianRelations.length > fallbackData.guardianRelations.length) {
          console.log('✅ Real master data fetched, updating...');
          setData(realData);
        } else {
          console.log('ℹ️  Using fallback data as real data is not available or incomplete');
        }
      } catch (error) {
        console.warn('⚠️  Failed to fetch real master data, continuing with fallback:', error);
      }
    };

    // Fetch real data after a short delay
    setTimeout(fetchRealData, 100);
  }, []);

  return {
    data,
    loading,
    error,
    refetch: fetchData
  };
};

// Individual hooks for specific master data
export const useClasses = () => {
  const [classes, setClasses] = useState<Class[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchClasses = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getClasses();
        setClasses(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch classes';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchClasses();
  }, []);

  return { classes, loading, error };
};

export const useSections = () => {
  const [sections, setSections] = useState<Section[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchSections = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getSections();
        setSections(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch sections';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchSections();
  }, []);

  return { sections, loading, error };
};

export const useAcademicYears = () => {
  const [academicYears, setAcademicYears] = useState<AcademicYear[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchAcademicYears = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getAcademicYears();
        setAcademicYears(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch academic years';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchAcademicYears();
  }, []);

  return { academicYears, loading, error };
};

export const useGuardianRelations = () => {
  const [guardianRelations, setGuardianRelations] = useState<GuardianRelation[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchGuardianRelations = async () => {
      try {
        setLoading(true);
        setError(null);
        const data = await MasterDataService.getGuardianRelations();
        setGuardianRelations(data);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to fetch guardian relations';
        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    fetchGuardianRelations();
  }, []);

  return { guardianRelations, loading, error };
};
